import ase
from ase.build import molecule
from ase.visualize import view
from ase.spacegroup import crystal

# 1. 生成C60分子
c60 = molecule('C60')

# 2. 打印基本信息
print(f"Number of atoms: {len(c60)}")
print("Atomic positions:")
for atom in c60:
    print(atom.position)

# 3. 可视化
view(c60)

# 4. 旋转和平移
# 旋转45度
c60.rotate('x', 45)
# 平移到原点
c60.translate([0, 0, 0])

# 5. 生成C60晶体结构
# 创建一个3x3x3的晶体结构
a = 10.0  # 晶胞参数
c60_crystal = crystal(['C'], [(0, 0, 0)], spacegroup=225, cellpar=[a, a, a, 90, 90, 90])
c60_crystal.extend(c60 * 3 * 3 * 3)

print("C60晶体结构生成完成")
